#!/usr/bin/env python3
"""
修复YOLO权重文件问题
"""

import os
import torch
import shutil
from pathlib import Path

def check_weight_file(filepath):
    """检查权重文件是否有效"""
    try:
        if not os.path.exists(filepath):
            return False, "文件不存在"
        
        # 检查文件大小
        size_mb = os.path.getsize(filepath) / (1024 * 1024)
        if size_mb < 1:
            return False, f"文件太小 ({size_mb:.1f} MB)"
        
        # 尝试加载权重
        checkpoint = torch.load(filepath, map_location='cpu')
        
        # 检查必要的键
        required_keys = ['model']
        missing_keys = [key for key in required_keys if key not in checkpoint]
        if missing_keys:
            return False, f"缺少键: {missing_keys}"
        
        return True, f"有效 ({size_mb:.1f} MB)"
        
    except Exception as e:
        return False, f"加载失败: {str(e)}"

def restore_from_backup():
    """从备份恢复权重"""
    backup_dir = "./(f)models/backup"
    models_dir = "./(f)models"
    
    if not os.path.exists(backup_dir):
        print("❌ 没有找到备份目录")
        return False
    
    restored = 0
    backup_files = ['yolov8n.pt.backup', 'yolov8s.pt.backup']
    
    for backup_file in backup_files:
        backup_path = os.path.join(backup_dir, backup_file)
        if os.path.exists(backup_path):
            # 恢复文件名
            original_name = backup_file.replace('.backup', '')
            target_path = os.path.join(models_dir, original_name)
            
            try:
                shutil.copy2(backup_path, target_path)
                
                # 验证恢复的文件
                is_valid, msg = check_weight_file(target_path)
                if is_valid:
                    print(f"✅ 恢复成功: {original_name} - {msg}")
                    restored += 1
                else:
                    print(f"❌ 恢复失败: {original_name} - {msg}")
                    os.remove(target_path)
            except Exception as e:
                print(f"❌ 恢复失败: {original_name} - {e}")
    
    return restored > 0

def download_fresh_weights():
    """下载全新的权重文件"""
    print("📥 下载全新权重文件...")
    
    try:
        from ultralytics import YOLO
        
        weights = ['yolov8n.pt', 'yolov8s.pt']
        success = 0
        
        for weight_name in weights:
            try:
                print(f"   下载 {weight_name}...")
                
                # 删除现有文件
                target_path = f"./(f)models/{weight_name}"
                if os.path.exists(target_path):
                    os.remove(target_path)
                
                # 下载新文件
                model = YOLO(weight_name)
                
                # 检查下载结果
                if os.path.exists(weight_name):
                    shutil.move(weight_name, target_path)
                
                # 验证
                is_valid, msg = check_weight_file(target_path)
                if is_valid:
                    print(f"   ✅ {weight_name}: {msg}")
                    success += 1
                else:
                    print(f"   ❌ {weight_name}: {msg}")
                    
            except Exception as e:
                print(f"   ❌ {weight_name}: {e}")
        
        return success > 0
        
    except ImportError:
        print("❌ ultralytics 未安装")
        return False

def main():
    """主修复流程"""
    print("🔧 YOLO权重文件修复工具")
    print("=" * 40)
    
    # 检查当前权重状态
    print("\n📋 检查当前权重文件:")
    weights_to_check = ['yolov8n.pt', 'yolov8s.pt']
    
    all_valid = True
    for weight_name in weights_to_check:
        filepath = f"./(f)models/{weight_name}"
        is_valid, msg = check_weight_file(filepath)
        
        if is_valid:
            print(f"✅ {weight_name}: {msg}")
        else:
            print(f"❌ {weight_name}: {msg}")
            all_valid = False
    
    if all_valid:
        print("\n🎯 所有权重文件都正常！")
        return
    
    # 尝试修复
    print(f"\n🔧 开始修复...")
    
    # 方法1: 从备份恢复
    print("\n1️⃣ 尝试从备份恢复...")
    if restore_from_backup():
        print("✅ 从备份恢复成功")
        
        # 重新检查
        print("\n📋 重新检查:")
        all_valid = True
        for weight_name in weights_to_check:
            filepath = f"./(f)models/{weight_name}"
            is_valid, msg = check_weight_file(filepath)
            print(f"{'✅' if is_valid else '❌'} {weight_name}: {msg}")
            if not is_valid:
                all_valid = False
        
        if all_valid:
            print("\n🎯 修复完成！")
            return
    
    # 方法2: 下载新权重
    print("\n2️⃣ 下载全新权重...")
    if download_fresh_weights():
        print("✅ 下载完成")
        
        # 最终检查
        print("\n📋 最终检查:")
        for weight_name in weights_to_check:
            filepath = f"./(f)models/{weight_name}"
            is_valid, msg = check_weight_file(filepath)
            print(f"{'✅' if is_valid else '❌'} {weight_name}: {msg}")
    
    print("\n🎯 修复流程完成！")

if __name__ == "__main__":
    main()
