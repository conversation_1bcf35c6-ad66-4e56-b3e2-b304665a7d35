#!/usr/bin/env python3
"""
修复YOLO模型类别数不匹配问题
解决训练完成后权重转换时的size mismatch错误
"""

import torch
import os
from ultralytics import YOLO
from Globals import *

def check_model_classes(model_path):
    """检查模型的类别数"""
    try:
        if model_path.endswith('.pt'):
            model = YOLO(model_path)
            nc = model.model.model[-1].nc if hasattr(model.model.model[-1], 'nc') else 80
            print(f"📊 {os.path.basename(model_path)} 类别数: {nc}")
            return nc
        elif model_path.endswith('.yaml'):
            # 读取YAML文件中的nc参数
            with open(model_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'nc:' in content:
                    for line in content.split('\n'):
                        if line.strip().startswith('nc:'):
                            nc = int(line.split(':')[1].strip())
                            print(f"📊 {os.path.basename(model_path)} 类别数: {nc}")
                            return nc
            return 80  # 默认值
    except Exception as e:
        print(f"❌ 检查 {model_path} 失败: {e}")
        return None

def fix_class_mismatch_in_weights(source_weights, target_nc, output_path):
    """修复权重文件中的类别数不匹配问题"""
    print(f"\n🔧 修复权重文件类别数不匹配...")
    print(f"源权重: {source_weights}")
    print(f"目标类别数: {target_nc}")
    print(f"输出路径: {output_path}")
    
    try:
        # 加载源权重
        if isinstance(source_weights, str):
            checkpoint = torch.load(source_weights, map_location='cpu')
            if isinstance(checkpoint, dict) and 'model' in checkpoint:
                state_dict = checkpoint['model'].state_dict() if hasattr(checkpoint['model'], 'state_dict') else checkpoint['model']
            else:
                state_dict = checkpoint
        else:
            # 如果是模型对象
            state_dict = source_weights.state_dict()
        
        # 找到需要修改的分类层
        modified_layers = []
        for key in list(state_dict.keys()):
            if 'cv3' in key and ('weight' in key or 'bias' in key):
                old_tensor = state_dict[key]
                
                if 'weight' in key and len(old_tensor.shape) == 4:
                    # 权重层: [out_channels, in_channels, h, w]
                    current_nc = old_tensor.shape[0]
                    if current_nc != target_nc:
                        new_shape = list(old_tensor.shape)
                        new_shape[0] = target_nc
                        new_tensor = torch.zeros(new_shape, dtype=old_tensor.dtype)
                        
                        # 复制现有权重
                        copy_nc = min(current_nc, target_nc)
                        new_tensor[:copy_nc] = old_tensor[:copy_nc]
                        
                        # 如果目标类别数更多，随机初始化新的类别
                        if target_nc > current_nc:
                            torch.nn.init.xavier_uniform_(new_tensor[current_nc:])
                        
                        state_dict[key] = new_tensor
                        modified_layers.append(f"{key}: {old_tensor.shape} → {new_tensor.shape}")
                
                elif 'bias' in key and len(old_tensor.shape) == 1:
                    # 偏置层: [out_channels]
                    current_nc = old_tensor.shape[0]
                    if current_nc != target_nc:
                        new_tensor = torch.zeros(target_nc, dtype=old_tensor.dtype)
                        
                        # 复制现有偏置
                        copy_nc = min(current_nc, target_nc)
                        new_tensor[:copy_nc] = old_tensor[:copy_nc]
                        
                        state_dict[key] = new_tensor
                        modified_layers.append(f"{key}: {old_tensor.shape} → {new_tensor.shape}")
        
        if modified_layers:
            print(f"✅ 修改了 {len(modified_layers)} 个层:")
            for layer in modified_layers:
                print(f"   {layer}")
            
            # 保存修复后的权重
            torch.save(state_dict, output_path)
            print(f"✅ 修复后的权重已保存到: {output_path}")
            return True
        else:
            print("ℹ️ 未发现需要修改的层")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_compatible_base_model():
    """创建与训练模型兼容的基础模型"""
    print(f"\n🔨 创建兼容的基础模型...")
    
    # 检查当前配置
    yaml_nc = check_model_classes(model_file)
    
    # 创建一个与YAML配置匹配的基础模型
    try:
        base_model = YOLO(model_file)
        compatible_base_path = "./(f)models/compatible_base_model.pt"
        base_model.save(compatible_base_path)
        print(f"✅ 兼容基础模型已创建: {compatible_base_path}")
        return compatible_base_path
    except Exception as e:
        print(f"❌ 创建兼容基础模型失败: {e}")
        return None

def main():
    """主函数：诊断和修复类别数不匹配问题"""
    print("🔍 YOLO模型类别数不匹配问题诊断和修复工具")
    print("=" * 60)
    
    # 1. 检查各个模型文件的类别数
    print("\n📋 检查模型文件类别数:")
    yaml_nc = check_model_classes(model_file)
    
    if os.path.exists(model_based_file):
        base_nc = check_model_classes(model_based_file)
    else:
        print(f"⚠️ 基础模型文件不存在: {model_based_file}")
        base_nc = None
    
    # 2. 检查是否存在不匹配
    if yaml_nc and base_nc and yaml_nc != base_nc:
        print(f"\n⚠️ 发现类别数不匹配!")
        print(f"   YAML配置: {yaml_nc} 类")
        print(f"   基础模型: {base_nc} 类")
        
        # 3. 提供解决方案
        print(f"\n💡 建议的解决方案:")
        print(f"   1. 使用兼容的基础模型 (推荐)")
        print(f"   2. 修改YAML配置文件的类别数")
        print(f"   3. 转换基础模型的类别数")
        
        # 创建兼容的基础模型
        compatible_base = create_compatible_base_model()
        if compatible_base:
            print(f"\n✅ 解决方案: 请在Globals.py中将model_based_file修改为:")
            print(f"   model_based_file = \"{compatible_base}\"")
    
    elif yaml_nc and base_nc and yaml_nc == base_nc:
        print(f"\n✅ 类别数匹配: {yaml_nc} 类")
    
    else:
        print(f"\n❓ 无法确定类别数，请检查文件是否存在")

if __name__ == "__main__":
    main()
