#!/usr/bin/env python3
"""
权重文件差异检查工具
比较不同权重文件的结构和参数维度
"""

import torch
import os
from pathlib import Path

def check_weight_file(weight_path, name=""):
    """检查单个权重文件的详细信息"""
    print(f"\n{'='*60}")
    print(f"检查权重文件: {name}")
    print(f"路径: {weight_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(weight_path):
        print(f"❌ 文件不存在: {weight_path}")
        return None
    
    try:
        # 加载权重文件
        if weight_path.endswith('.pt'):
            checkpoint = torch.load(weight_path, map_location='cpu')
            if isinstance(checkpoint, dict):
                if 'model' in checkpoint:
                    state_dict = checkpoint['model'].state_dict() if hasattr(checkpoint['model'], 'state_dict') else checkpoint['model']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint.state_dict() if hasattr(checkpoint, 'state_dict') else checkpoint
        elif weight_path.endswith('.pth'):
            state_dict = torch.load(weight_path, map_location='cpu')
        else:
            print(f"❌ 不支持的文件格式: {weight_path}")
            return None
            
        print(f"✅ 权重文件加载成功")
        print(f"📊 总参数数量: {len(state_dict)}")
        
        # 分析关键层
        detection_layers = []
        backbone_layers = []
        neck_layers = []
        
        for key, tensor in state_dict.items():
            if 'model.' in key:
                layer_num = key.split('.')[1]
                if layer_num.isdigit():
                    layer_num = int(layer_num)
                    if layer_num >= 40:  # 检测头相关层
                        detection_layers.append((key, tensor.shape))
                    elif layer_num >= 20:  # neck层
                        neck_layers.append((key, tensor.shape))
                    else:  # backbone层
                        backbone_layers.append((key, tensor.shape))
        
        print(f"\n🔍 层结构分析:")
        print(f"   Backbone层: {len(backbone_layers)}")
        print(f"   Neck层: {len(neck_layers)}")
        print(f"   Detection层: {len(detection_layers)}")
        
        # 显示检测头关键层
        print(f"\n🎯 检测头关键层 (layer >= 40):")
        for key, shape in detection_layers[:10]:  # 只显示前10个
            print(f"   {key}: {shape}")
        if len(detection_layers) > 10:
            print(f"   ... 还有 {len(detection_layers) - 10} 个检测层")
            
        # 检查特殊层
        special_layers = []
        for key in state_dict.keys():
            if any(x in key.lower() for x in ['detect', 'teacher', 'student', 'distill']):
                special_layers.append((key, state_dict[key].shape))
        
        if special_layers:
            print(f"\n🔬 特殊层 (蒸馏相关):")
            for key, shape in special_layers:
                print(f"   {key}: {shape}")
        
        return state_dict
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def compare_weights(weight1_path, weight2_path, name1="权重1", name2="权重2"):
    """比较两个权重文件的差异"""
    print(f"\n{'='*80}")
    print(f"权重文件对比: {name1} vs {name2}")
    print(f"{'='*80}")
    
    state_dict1 = check_weight_file(weight1_path, name1)
    state_dict2 = check_weight_file(weight2_path, name2)
    
    if state_dict1 is None or state_dict2 is None:
        print("❌ 无法比较，某个权重文件加载失败")
        return
    
    # 比较键的差异
    keys1 = set(state_dict1.keys())
    keys2 = set(state_dict2.keys())
    
    common_keys = keys1 & keys2
    only_in_1 = keys1 - keys2
    only_in_2 = keys2 - keys1
    
    print(f"\n📊 键对比结果:")
    print(f"   共同键: {len(common_keys)}")
    print(f"   仅在{name1}: {len(only_in_1)}")
    print(f"   仅在{name2}: {len(only_in_2)}")
    
    if only_in_1:
        print(f"\n🔍 仅在{name1}中的键:")
        for key in sorted(list(only_in_1)[:10]):
            print(f"   {key}: {state_dict1[key].shape}")
        if len(only_in_1) > 10:
            print(f"   ... 还有 {len(only_in_1) - 10} 个")
    
    if only_in_2:
        print(f"\n🔍 仅在{name2}中的键:")
        for key in sorted(list(only_in_2)[:10]):
            print(f"   {key}: {state_dict2[key].shape}")
        if len(only_in_2) > 10:
            print(f"   ... 还有 {len(only_in_2) - 10} 个")
    
    # 比较共同键的维度差异
    shape_diff = []
    for key in common_keys:
        if state_dict1[key].shape != state_dict2[key].shape:
            shape_diff.append((key, state_dict1[key].shape, state_dict2[key].shape))
    
    if shape_diff:
        print(f"\n⚠️ 维度不匹配的层 ({len(shape_diff)}个):")
        for key, shape1, shape2 in shape_diff[:10]:
            print(f"   {key}: {shape1} vs {shape2}")
        if len(shape_diff) > 10:
            print(f"   ... 还有 {len(shape_diff) - 10} 个维度不匹配")
    else:
        print(f"\n✅ 所有共同层的维度都匹配!")

def convert_fruit_to_coco_weights(fruit_path, output_path):
    """将3类水果权重转换为80类COCO权重"""
    print(f"\n🔄 转换权重: 3类 → 80类")
    print(f"输入: {fruit_path}")
    print(f"输出: {output_path}")

    if not os.path.exists(fruit_path):
        print(f"❌ 源文件不存在: {fruit_path}")
        return False

    try:
        # 加载水果权重
        fruit_weights = torch.load(fruit_path, map_location='cpu')
        print(f"✅ 加载水果权重成功")

        # 需要修改的层
        layers_to_modify = [
            'model.model.45.cv3.0.2.weight',
            'model.model.45.cv3.0.2.bias',
            'model.model.45.cv3.1.2.weight',
            'model.model.45.cv3.1.2.bias',
            'model.model.45.cv3.2.2.weight',
            'model.model.45.cv3.2.2.bias'
        ]

        # 转换权重
        for layer_name in layers_to_modify:
            if layer_name in fruit_weights:
                old_tensor = fruit_weights[layer_name]
                if 'weight' in layer_name:
                    # 权重层：从[3, 128, 1, 1] → [80, 128, 1, 1]
                    new_shape = list(old_tensor.shape)
                    new_shape[0] = 80  # 改为80类
                    new_tensor = torch.zeros(new_shape, dtype=old_tensor.dtype)
                    # 复制前3个类别的权重到新张量
                    new_tensor[:3] = old_tensor
                    # 其余77个类别使用随机初始化
                    torch.nn.init.xavier_uniform_(new_tensor[3:])
                else:
                    # 偏置层：从[3] → [80]
                    new_tensor = torch.zeros(80, dtype=old_tensor.dtype)
                    new_tensor[:3] = old_tensor
                    # 其余77个类别的偏置初始化为0

                fruit_weights[layer_name] = new_tensor
                print(f"✅ 转换层: {layer_name} {old_tensor.shape} → {new_tensor.shape}")

        # 保存转换后的权重
        torch.save(fruit_weights, output_path)
        print(f"✅ 转换完成，保存到: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

if __name__ == "__main__":
    # 权重文件路径
    fruit_weight = "./(f)models/yolov8_teacher_prepare-fruit.pth"
    coco_weight = "./(f)models/yolov8_teacher_prepare-coco.pth"
    standard_weight = "./(f)models/yolov8s.pt"
    converted_weight = "./(f)models/yolov8_teacher_prepare-fruit_to_coco.pth"

    print("🔍 YOLO权重文件差异分析工具")
    print("="*80)

    # 检查各个权重文件
    check_weight_file(fruit_weight, "水果蒸馏权重")
    check_weight_file(coco_weight, "COCO蒸馏权重")
    check_weight_file(standard_weight, "标准YOLOv8s权重")

    # 比较权重文件
    if os.path.exists(fruit_weight) and os.path.exists(coco_weight):
        compare_weights(fruit_weight, coco_weight, "水果权重", "COCO权重")

    if os.path.exists(fruit_weight) and os.path.exists(standard_weight):
        compare_weights(fruit_weight, standard_weight, "水果权重", "标准权重")

    # 转换权重
    print(f"\n{'='*80}")
    print("🔄 权重转换")
    print(f"{'='*80}")

    if convert_fruit_to_coco_weights(fruit_weight, converted_weight):
        print(f"\n✅ 现在可以使用转换后的权重文件:")
        print(f"   {converted_weight}")
        print(f"   配合 coco128.yaml 数据集使用")
